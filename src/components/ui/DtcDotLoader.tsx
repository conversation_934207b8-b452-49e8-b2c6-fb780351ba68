"use client";

import { ComponentProps, useCallback, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";

type DtcDotLoaderProps = {
    isPlaying?: boolean;
    duration?: number;
    repeatCount?: number;
    onComplete?: () => void;
    dotClassName?: string;
} & ComponentProps<"div">;

// DTC letter patterns on a 7x7 grid (49 dots total)
const DTC_FRAMES = [
    // Frame 1: Show D
    [
        0, 1, 2, 3,
        7, 11,
        14, 18,
        21, 25,
        28, 32,
        35, 39,
        42, 43, 44, 45
    ],
    // Frame 2: Show T
    [
        0, 1, 2, 3, 4, 5, 6,
        10,
        17,
        24,
        31,
        38,
        45
    ],
    // Frame 3: Show C
    [
        1, 2, 3, 4, 5,
        7, 13,
        14,
        21,
        28,
        35, 41,
        43, 44, 45, 46, 47
    ],
    // Frame 4: Show all DTC together (compact)
    [
        // D (left)
        0, 1, 2,
        7, 9,
        14, 16,
        21, 23,
        28, 30,
        35, 37,
        42, 43, 44,
        // T (middle)
        3, 4, 5,
        11,
        18,
        25,
        32,
        39,
        46,
        // C (right)
        15, 16, 17,
        22, 24,
        29,
        36,
        43, 45,
        50, 51, 52
    ].filter(i => i < 49), // Ensure we don't exceed grid bounds
];

export const DtcDotLoader = ({
    isPlaying = true,
    duration = 800,
    dotClassName,
    className,
    repeatCount = -1,
    onComplete,
    ...props
}: DtcDotLoaderProps) => {
    const gridRef = useRef<HTMLDivElement>(null);
    const currentIndex = useRef(0);
    const repeats = useRef(0);
    const interval = useRef<NodeJS.Timeout | null>(null);

    const applyFrameToDots = useCallback(
        (dots: HTMLDivElement[], frameIndex: number) => {
            const frame = DTC_FRAMES[frameIndex];
            if (!frame) return;

            dots.forEach((dot, index) => {
                const isActive = frame.includes(index);
                dot.classList.toggle("active", isActive);
                
                // Apply DTC colors
                if (isActive) {
                    dot.style.backgroundColor = "var(--emerald)";
                    dot.style.opacity = "1";
                } else {
                    dot.style.backgroundColor = "var(--color-border)";
                    dot.style.opacity = "0.3";
                }
            });
        },
        []
    );

    useEffect(() => {
        currentIndex.current = 0;
        repeats.current = 0;
    }, []);

    useEffect(() => {
        if (isPlaying) {
            if (currentIndex.current >= DTC_FRAMES.length) {
                currentIndex.current = 0;
            }
            const dotElements = gridRef.current?.children;
            if (!dotElements) return;
            const dots = Array.from(dotElements) as HTMLDivElement[];
            
            // Initial frame
            applyFrameToDots(dots, currentIndex.current);
            
            interval.current = setInterval(() => {
                currentIndex.current = (currentIndex.current + 1) % DTC_FRAMES.length;
                applyFrameToDots(dots, currentIndex.current);
                
                if (currentIndex.current === 0) {
                    if (repeatCount !== -1 && repeats.current + 1 >= repeatCount) {
                        if (interval.current) clearInterval(interval.current);
                        onComplete?.();
                        return;
                    }
                    repeats.current++;
                }
            }, duration);
        } else {
            if (interval.current) clearInterval(interval.current);
        }

        return () => {
            if (interval.current) clearInterval(interval.current);
        };
    }, [isPlaying, applyFrameToDots, duration, repeatCount, onComplete]);

    return (
        <div 
            {...props} 
            ref={gridRef} 
            className={cn("grid w-fit grid-cols-7 gap-1", className)}
        >
            {Array.from({ length: 49 }).map((_, i) => (
                <div 
                    key={i} 
                    className={cn(
                        "h-2 w-2 rounded-sm transition-all duration-200",
                        dotClassName
                    )}
                    style={{
                        backgroundColor: "var(--color-border)",
                        opacity: "0.3"
                    }}
                />
            ))}
        </div>
    );
};

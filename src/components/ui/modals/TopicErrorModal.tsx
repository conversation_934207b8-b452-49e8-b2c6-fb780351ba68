"use client";

import { motion, AnimatePresence } from "framer-motion";
import { X, AlertTriangle, BookOpen, Shield, Cloud, Database, Settings, Award } from "lucide-react";

interface TopicErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  topic: string;
  errorMessage: string;
}

export default function TopicErrorModal({ isOpen, onClose, topic, errorMessage }: TopicErrorModalProps) {
  const suggestions = [
    { icon: Shield, text: "Cybersecurity & Privacy", example: "GDPR Compliance" },
    { icon: Cloud, text: "Cloud Computing", example: "AWS Security" },
    { icon: Database, text: "Data Management", example: "Data Governance" },
    { icon: Settings, text: "IT Operations", example: "Network Security" },
    { icon: BookOpen, text: "Project Management", example: "Agile Methodology" },
    { icon: Award, text: "Professional Development", example: "Risk Management" }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div className="dtc-card rounded-2xl p-8 max-w-md w-full bg-white border border-[var(--color-border)] shadow-xl">
              {/* Header */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="w-6 h-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-[var(--charcoal)]">Topic Not Supported</h3>
                    <p className="text-sm text-[var(--grey)]">Outside certification scope</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-[var(--color-muted)] rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-[var(--grey)]" />
                </button>
              </div>

              {/* Error Message */}
              <div className="mb-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <p className="text-red-800 text-sm">
                    <span className="font-medium">"{topic}"</span> is not related to professional certifications.
                  </p>
                </div>
                <p className="text-[var(--grey)] text-sm">
                  Our AI learning system is designed specifically for professional certification topics. 
                  Please try one of these areas instead:
                </p>
              </div>

              {/* Suggestions */}
              <div className="space-y-3 mb-6">
                {suggestions.map((suggestion, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3 p-3 bg-[var(--color-muted)] rounded-lg hover:bg-[var(--color-muted)]/80 transition-colors"
                  >
                    <div className="w-8 h-8 bg-[var(--emerald)] rounded-lg flex items-center justify-center">
                      <suggestion.icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-[var(--charcoal)] text-sm">{suggestion.text}</p>
                      <p className="text-xs text-[var(--grey)]">e.g., {suggestion.example}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-3 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition-colors font-medium"
                >
                  Try Again
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

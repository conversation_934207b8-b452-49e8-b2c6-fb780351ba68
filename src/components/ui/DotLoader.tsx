"use client";

import { ComponentProps, useCallback, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";

type DotLoaderProps = {
    frames: number[][];
    dotClassName?: string;
    isPlaying?: boolean;
    duration?: number;
    repeatCount?: number;
    onComplete?: () => void;
} & ComponentProps<"div">;

export const DotLoader = ({
    frames,
    isPlaying = true,
    duration = 100,
    dotClassName,
    className,
    repeatCount = -1,
    onComplete,
    ...props
}: DotLoaderProps) => {
    const gridRef = useRef<HTMLDivElement>(null);
    const currentIndex = useRef(0);
    const repeats = useRef(0);
    const interval = useRef<NodeJS.Timeout>(null);

    const applyFrameToDots = useCallback(
        (dots: HTMLDivElement[], frameIndex: number) => {
            const frame = frames[frameIndex];
            if (!frame) return;

            dots.forEach((dot, index) => {
                dot.classList.toggle("active", frame.includes(index));
            });
        },
        [frames],
    );

    useEffect(() => {
        currentIndex.current = 0;
        repeats.current = 0;
    }, [frames]);

    useEffect(() => {
        if (isPlaying) {
            if (currentIndex.current >= frames.length) {
                currentIndex.current = 0;
            }
            const dotElements = gridRef.current?.children;
            if (!dotElements) return;
            const dots = Array.from(dotElements) as HTMLDivElement[];
            interval.current = setInterval(() => {
                applyFrameToDots(dots, currentIndex.current);
                if (currentIndex.current + 1 >= frames.length) {
                    if (repeatCount != -1 && repeats.current + 1 >= repeatCount) {
                        clearInterval(interval.current!);
                        onComplete?.();
                    }
                    repeats.current++;
                }
                currentIndex.current = (currentIndex.current + 1) % frames.length;
            }, duration);
        } else {
            if (interval.current) clearInterval(interval.current);
        }

        return () => {
            if (interval.current) clearInterval(interval.current);
        };
    }, [frames, isPlaying, applyFrameToDots, duration, repeatCount, onComplete]);

    return (
        <div {...props} ref={gridRef} className={cn("grid w-fit grid-cols-7 gap-0.5", className)}>
            {Array.from({ length: 49 }).map((_, i) => (
                <div 
                    key={i} 
                    className={cn(
                        "h-1.5 w-1.5 rounded-sm bg-[var(--color-border)] transition-colors duration-200",
                        "[&.active]:bg-[var(--emerald)]",
                        dotClassName
                    )} 
                />
            ))}
        </div>
    );
};

// DTC Letter Frames - Each letter displayed one by one
export const dtcFrames = [
    // D
    [1, 2, 3, 8, 11, 15, 18, 22, 25, 29, 32, 36, 37, 38],
    // T  
    [1, 2, 3, 4, 5, 9, 16, 23, 30, 37, 44],
    // C
    [2, 3, 4, 8, 12, 15, 22, 29, 36, 37, 38],
    // Clear
    [],
    // D again
    [1, 2, 3, 8, 11, 15, 18, 22, 25, 29, 32, 36, 37, 38],
    // T again
    [1, 2, 3, 4, 5, 9, 16, 23, 30, 37, 44],
    // C again
    [2, 3, 4, 8, 12, 15, 22, 29, 36, 37, 38],
    // All together
    [1, 2, 3, 4, 5, 8, 9, 11, 12, 15, 16, 18, 22, 23, 25, 29, 30, 32, 36, 37, 38, 44],
];

// DTC Loader Component
export const DtcLoader = ({ className, ...props }: Omit<DotLoaderProps, 'frames'>) => {
    return (
        <DotLoader
            frames={dtcFrames}
            duration={500}
            className={className}
            {...props}
        />
    );
};

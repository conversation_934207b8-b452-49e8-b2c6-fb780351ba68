"use client";

import {
  useMotionValueEvent,
  useScroll,
  useTransform,
  motion,
} from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import { 
  Clock, 
  Target, 
  BookOpen, 
  CheckCircle2, 
  Play, 
  Star,
  Trophy,
  Lightbulb
} from "lucide-react";

interface LearningStep {
  id: string;
  title: string;
  description: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: string;
  prerequisites: string[];
  keyTopics: string[];
  practicalExercises: string[];
  resources: string[];
  icon?: string;
}

interface Milestone {
  step: number;
  title: string;
  description: string;
}

interface LearningTimelineProps {
  learningPath: LearningStep[];
  milestones: Milestone[];
}

const difficultyColors = {
  beginner: "bg-[var(--lime-green)] text-white",
  intermediate: "bg-[var(--bright-green)] text-white", 
  advanced: "bg-[var(--emerald)] text-white"
};

const difficultyIcons = {
  beginner: Star,
  intermediate: Target,
  advanced: Trophy
};

export default function LearningTimeline({ learningPath, milestones }: LearningTimelineProps) {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 10%", "end 50%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);

  return (
    <div
      className="w-full bg-transparent font-sans"
      ref={containerRef}
    >
      <div className="max-w-7xl mx-auto py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-[var(--charcoal)] mb-4 font-display">
            Your Single-Session Learning Path
          </h2>
          <p className="text-[var(--grey)] text-lg max-w-2xl mx-auto">
            Complete this focused learning journey in one sitting. Each step builds upon the previous one for maximum retention.
          </p>
        </motion.div>
      </div>

      <div ref={ref} className="relative max-w-7xl mx-auto pb-20">
        {learningPath.map((step, index) => {
          const DifficultyIcon = difficultyIcons[step.difficulty];
          const isMilestone = milestones.some(m => m.step === index + 1);
          const milestone = milestones.find(m => m.step === index + 1);
          
          return (
            <motion.div
              key={step.id}
              className="flex justify-start pt-10 md:pt-20 md:gap-10"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="sticky flex flex-col md:flex-row z-40 items-center top-40 self-start max-w-xs lg:max-w-sm md:w-full">
                <div className="h-12 absolute left-3 md:left-3 w-12 rounded-full bg-white border-4 border-[var(--emerald)] flex items-center justify-center shadow-lg">
                  <div className={cn(
                    "h-6 w-6 rounded-full flex items-center justify-center",
                    difficultyColors[step.difficulty]
                  )}>
                    <DifficultyIcon className="w-3 h-3" />
                  </div>
                </div>
                <h3 className="hidden md:block text-lg md:pl-20 md:text-2xl font-bold text-[var(--charcoal)] leading-tight">
                  Step {index + 1}
                </h3>
              </div>

              <div className="relative pl-20 pr-4 md:pl-4 w-full">
                <h3 className="md:hidden block text-xl mb-4 text-left font-bold text-[var(--charcoal)]">
                  Step {index + 1}
                </h3>
                
                {/* Main Content Card */}
                <div className="dtc-card rounded-2xl p-6 md:p-8 mb-6 bg-white border border-[var(--color-border)] hover:shadow-lg transition-shadow">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h4 className="text-xl md:text-2xl font-semibold text-[var(--charcoal)] mb-2">
                        {step.title}
                      </h4>
                      <div className="flex items-center gap-3 mb-3">
                        <span className={cn(
                          "px-3 py-1 rounded-full text-xs font-medium",
                          difficultyColors[step.difficulty]
                        )}>
                          {step.difficulty.charAt(0).toUpperCase() + step.difficulty.slice(1)}
                        </span>
                        <div className="flex items-center gap-1 text-[var(--grey)]">
                          <Clock className="w-4 h-4" />
                          <span className="text-sm">{step.estimatedTime}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-[var(--grey)] mb-6 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Key Topics */}
                  {step.keyTopics.length > 0 && (
                    <div className="mb-6">
                      <h5 className="flex items-center gap-2 font-semibold text-[var(--charcoal)] mb-3">
                        <BookOpen className="w-4 h-4 text-[var(--emerald)]" />
                        Key Topics
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {step.keyTopics.map((topic, topicIndex) => (
                          <span
                            key={topicIndex}
                            className="px-3 py-1 bg-[var(--color-muted)] text-[var(--charcoal)] rounded-full text-sm"
                          >
                            {topic}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Practical Exercises */}
                  {step.practicalExercises.length > 0 && (
                    <div className="mb-6">
                      <h5 className="flex items-center gap-2 font-semibold text-[var(--charcoal)] mb-3">
                        <Play className="w-4 h-4 text-[var(--bright-green)]" />
                        Practical Exercises
                      </h5>
                      <ul className="space-y-2">
                        {step.practicalExercises.map((exercise, exerciseIndex) => (
                          <li key={exerciseIndex} className="flex items-start gap-2">
                            <CheckCircle2 className="w-4 h-4 text-[var(--bright-green)] mt-0.5 flex-shrink-0" />
                            <span className="text-[var(--grey)] text-sm">{exercise}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Prerequisites */}
                  {step.prerequisites.length > 0 && (
                    <div className="mb-4">
                      <h5 className="flex items-center gap-2 font-semibold text-[var(--charcoal)] mb-2">
                        <Target className="w-4 h-4 text-[var(--emerald)]" />
                        Prerequisites
                      </h5>
                      <p className="text-[var(--grey)] text-sm">
                        {step.prerequisites.join(", ")}
                      </p>
                    </div>
                  )}
                </div>

                {/* Milestone Card */}
                {isMilestone && milestone && (
                  <motion.div
                    className="dtc-card rounded-xl p-4 bg-gradient-to-r from-[var(--emerald)] to-[var(--emerald-deep)] text-white mb-6"
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <Trophy className="w-5 h-5" />
                      </div>
                      <div>
                        <h5 className="font-semibold">{milestone.title}</h5>
                        <p className="text-white/90 text-sm">{milestone.description}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          );
        })}
        
        {/* Animated Progress Line */}
        <div
          style={{
            height: height + "px",
          }}
          className="absolute md:left-8 left-8 top-0 overflow-hidden w-[3px] bg-gradient-to-b from-transparent via-[var(--color-border)] to-transparent"
        >
          <motion.div
            style={{
              height: heightTransform,
              opacity: opacityTransform,
            }}
            className="absolute inset-x-0 top-0 w-[3px] bg-gradient-to-b from-[var(--emerald)] via-[var(--bright-green)] to-[var(--lime-green)] rounded-full"
          />
        </div>
      </div>
    </div>
  );
}

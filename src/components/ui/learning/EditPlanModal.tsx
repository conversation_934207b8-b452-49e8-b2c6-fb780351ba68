"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Save, Plus, Trash2, Edit3 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LearningStep {
  id: string;
  title: string;
  description: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: string;
  prerequisites: string[];
  keyTopics: string[];
  practicalExercises: string[];
  resources: string[];
  icon?: string;
}

interface EditPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  learningPath: LearningStep[];
  onSave: (updatedPath: LearningStep[]) => void;
}

export default function EditPlanModal({ isOpen, onClose, learningPath, onSave }: EditPlanModalProps) {
  const [editedPath, setEditedPath] = useState<LearningStep[]>(learningPath);
  const [editingStep, setEditingStep] = useState<string | null>(null);

  const updateStep = (stepId: string, field: keyof LearningStep, value: any) => {
    setEditedPath(prev => prev.map(step => 
      step.id === stepId ? { ...step, [field]: value } : step
    ));
  };

  const addStep = () => {
    const newStep: LearningStep = {
      id: `step_${Date.now()}`,
      title: "New Learning Step",
      description: "Add your description here...",
      difficulty: "beginner",
      estimatedTime: "10 minutes",
      prerequisites: [],
      keyTopics: [],
      practicalExercises: [],
      resources: []
    };
    setEditedPath(prev => [...prev, newStep]);
  };

  const removeStep = (stepId: string) => {
    setEditedPath(prev => prev.filter(step => step.id !== stepId));
  };

  const handleSave = () => {
    onSave(editedPath);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-[var(--emerald)] to-[var(--emerald-deep)] text-white p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Edit3 className="w-6 h-6" />
                <h2 className="text-xl font-semibold">Edit Learning Plan</h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/10 rounded-lg transition"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="space-y-6">
              {editedPath.map((step, index) => (
                <div key={step.id} className="dtc-card rounded-xl p-6 border border-[var(--color-border)]">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold text-[var(--charcoal)]">
                      Step {index + 1}
                    </h3>
                    <button
                      onClick={() => removeStep(step.id)}
                      className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="grid gap-4">
                    {/* Title */}
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                        Title
                      </label>
                      <input
                        type="text"
                        value={step.title}
                        onChange={(e) => updateStep(step.id, 'title', e.target.value)}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent"
                      />
                    </div>

                    {/* Description */}
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                        Description
                      </label>
                      <textarea
                        value={step.description}
                        onChange={(e) => updateStep(step.id, 'description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent resize-none"
                      />
                    </div>

                    {/* Difficulty and Time */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                          Difficulty
                        </label>
                        <select
                          value={step.difficulty}
                          onChange={(e) => updateStep(step.id, 'difficulty', e.target.value)}
                          className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent"
                        >
                          <option value="beginner">Beginner</option>
                          <option value="intermediate">Intermediate</option>
                          <option value="advanced">Advanced</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                          Estimated Time
                        </label>
                        <input
                          type="text"
                          value={step.estimatedTime}
                          onChange={(e) => updateStep(step.id, 'estimatedTime', e.target.value)}
                          className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent"
                        />
                      </div>
                    </div>

                    {/* Key Topics */}
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                        Key Topics (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={step.keyTopics.join(', ')}
                        onChange={(e) => updateStep(step.id, 'keyTopics', e.target.value.split(',').map(t => t.trim()).filter(Boolean))}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent"
                      />
                    </div>

                    {/* Practical Exercises */}
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-2">
                        Practical Exercises (comma-separated)
                      </label>
                      <textarea
                        value={step.practicalExercises.join(', ')}
                        onChange={(e) => updateStep(step.id, 'practicalExercises', e.target.value.split(',').map(t => t.trim()).filter(Boolean))}
                        rows={2}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)] focus:border-transparent resize-none"
                      />
                    </div>
                  </div>
                </div>
              ))}

              {/* Add Step Button */}
              <button
                onClick={addStep}
                className="w-full p-4 border-2 border-dashed border-[var(--color-border)] rounded-xl hover:border-[var(--emerald)] hover:bg-[var(--emerald)]/5 transition flex items-center justify-center gap-2 text-[var(--grey)] hover:text-[var(--emerald)]"
              >
                <Plus className="w-5 h-5" />
                Add New Step
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-[var(--color-border)] p-6 bg-[var(--color-muted)]">
            <div className="flex items-center justify-end gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-[var(--grey)] hover:text-[var(--charcoal)] transition"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="inline-flex items-center gap-2 px-6 py-2 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition font-medium"
              >
                <Save className="w-4 h-4" />
                Save Changes
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}

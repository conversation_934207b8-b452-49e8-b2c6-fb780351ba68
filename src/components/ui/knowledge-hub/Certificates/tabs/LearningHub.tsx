"use client";

import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useParams, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { getCertificateTaxonomy, type CertificateTaxonomy } from "@/Services/questionsService";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";
import TextGenerateEffect from "@/components/ui/effects/TextGenerateEffect";
import { DtcDotLoader } from "@/components/ui/DtcDotLoader";
import TopicErrorModal from "@/components/ui/modals/TopicErrorModal";

// Using the shared TextGenerateEffect component

export default function LearningHub() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const framework = Array.isArray(params?.framework)
    ? params?.framework[0]
    : (params?.framework as string | undefined);

  const [topic, setTopic] = useState("");
  const [taxonomy, setTaxonomy] = useState<CertificateTaxonomy | null>(null);
  const [loading, setLoading] = useState(false);
  const [certificateDetails, setCertificateDetails] = useState<CertificateRecord | null>(null);

  const [intensity, setIntensity] = useState<"detailed" | "general" | "simple">("general");
  const [focusInput, setFocusInput] = useState("");
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [placeholderIndex, setPlaceholderIndex] = useState(0);

  // Error modal state
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorTopic, setErrorTopic] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [aiLoading, setAiLoading] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState("");

  useEffect(() => {
    let isMounted = true;
    async function loadData() {
      if (!framework) return;
      setLoading(true);
      try {
        const [taxonomyData, certData] = await Promise.all([
          getCertificateTaxonomy(framework),
          getCertificate(framework)
        ]);
        if (isMounted) {
          setTaxonomy(taxonomyData);
          setCertificateDetails(certData);
        }
      } finally {
        if (isMounted) setLoading(false);
      }
    }
    loadData();
    return () => {
      isMounted = false;
    };
  }, [framework]);

  const groups = taxonomy?.groups ?? [];

  const generateLearningPlan = async (topicName: string) => {
    if (!framework) return;

    // Comprehensive validation for non-certificate topics
    const nonCertificateKeywords = [
      // Food & Cooking
      'cooking', 'recipe', 'food', 'restaurant', 'kitchen', 'chef', 'baking', 'meal', 'diet', 'nutrition',
      // Sports & Games
      'sports', 'football', 'basketball', 'soccer', 'tennis', 'golf', 'baseball', 'hockey', 'swimming', 'running',
      'game', 'gaming', 'video game', 'board game', 'card game', 'puzzle',
      // Entertainment
      'entertainment', 'movie', 'film', 'music', 'song', 'tv show', 'television', 'celebrity', 'actor', 'singer',
      'concert', 'theater', 'dance', 'comedy', 'drama',
      // Hobbies & Arts
      'hobby', 'craft', 'art', 'painting', 'drawing', 'photography', 'sculpture', 'pottery', 'knitting', 'sewing',
      // Travel & Tourism
      'travel', 'vacation', 'tourism', 'holiday', 'trip', 'destination', 'hotel', 'flight', 'cruise',
      // Fashion & Beauty
      'fashion', 'clothing', 'style', 'makeup', 'beauty', 'hair', 'nail', 'jewelry', 'accessory',
      // Personal & Relationships
      'personal', 'relationship', 'dating', 'family', 'marriage', 'wedding', 'love', 'friendship',
      // Health & Fitness (non-certification)
      'workout', 'exercise', 'gym', 'fitness', 'weight loss', 'muscle', 'yoga', 'meditation',
      // General Life
      'shopping', 'money saving', 'budgeting', 'home improvement', 'gardening', 'pet', 'animal'
    ];

    const topicLower = topicName.toLowerCase();
    const isNonCertificate = nonCertificateKeywords.some(keyword =>
      topicLower.includes(keyword)
    );

    // Additional check for certificate-related keywords
    const certificateKeywords = [
      'certification', 'certified', 'exam', 'cissp', 'cism', 'ceh', 'comptia', 'aws', 'azure', 'gcp',
      'pmp', 'agile', 'scrum', 'gdpr', 'ccpa', 'cipp', 'privacy', 'compliance', 'security',
      'network', 'cloud', 'data', 'analytics', 'governance', 'risk', 'audit', 'iso', 'nist',
      'microsoft', 'cisco', 'oracle', 'salesforce', 'vmware', 'kubernetes', 'docker'
    ];

    const hasCertificateKeywords = certificateKeywords.some(keyword =>
      topicLower.includes(keyword)
    );

    if (isNonCertificate) {
      setErrorTopic(topicName);
      setErrorMessage(`"${topicName}" doesn't appear to be related to professional certifications. Please enter a topic related to IT, cybersecurity, cloud computing, data science, project management, or other professional certification areas.`);
      setShowErrorModal(true);
      return;
    }

    // If topic doesn't contain obvious non-certificate keywords but also doesn't contain certificate keywords,
    // let the AI do the final validation
    if (!hasCertificateKeywords && topicName.length > 3) {
      console.log(`Topic "${topicName}" will be validated by AI for certificate relevance`);
    }

    setSelectedTopic(topicName);
    setAiLoading(true);

    try {
      // Call AI to generate learning plan
      const response = await fetch('/api/ai/learning/plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic: topicName,
          intensity,
          focusAreas,
          framework,
          certificateDetails: certificateDetails ? {
            name: certificateDetails.name,
            provider: certificateDetails.provider,
            description: certificateDetails.description,
            domain: certificateDetails.domain,
            questionType: certificateDetails.questionType
          } : undefined
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate learning plan');
      }

      if (!result.success) {
        const errorMsg = result.error || 'AI rejected the topic as not certificate-related';
        setErrorTopic(topicName);
        setErrorMessage(errorMsg);
        setShowErrorModal(true);
        return;
      }

      // Store the learning plan data in sessionStorage for immediate access
      sessionStorage.setItem('currentLearningPlan', JSON.stringify(result.data));

      // Navigate to the learning concept page after successful generation
      const conceptName = encodeURIComponent(`C${topicName}`);
      router.push(`/${locale}/dashboard/knowledge-hub/certificates/${framework}/${conceptName}`);

    } catch (error) {
      console.error('Error generating learning plan:', error);
      setErrorTopic(topicName);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to generate learning plan');
      setShowErrorModal(true);
    } finally {
      setAiLoading(false);
    }
  };

  const rotatingExamples = useMemo(() => {
    if (!groups || groups.length === 0) return ["Start with your category name"];
    return groups;
  }, [groups]);

  useEffect(() => {
    if (!rotatingExamples || rotatingExamples.length <= 1) return;
    setPlaceholderIndex(0);
    const interval = setInterval(() => {
      setPlaceholderIndex((prev) => (prev + 1) % rotatingExamples.length);
    }, 2400);
    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rotatingExamples.length]);

  return (
    <div className="w-full">
      <div className="max-w-5xl mx-auto px-6 py-10">
        {/* Header */}
        <div className="text-center mb-10">
          <TextGenerateEffect
            words="What would you like to study today?"
            className="font-display"
          />
          <p className="mt-3 text-grey max-w-2xl mx-auto">
            Enter a certification-related topic (IT, cybersecurity, cloud, data science, etc.). You can also pick from your configured categories.
          </p>
        </div>

        {/* Centered Topic Input */}
        <div className="relative">
          <div
            className={cn(
              "dtc-card rounded-2xl p-6 md:p-8 bg-gradient-to-b from-white to-white/90",
              "border border-[var(--color-border)]"
            )}
          >
            <div className="flex flex-col items-center justify-center min-h-[32vh] gap-6">
              <label
                htmlFor="topic"
                className="text-sm font-medium text-grey select-none"
              >
                Topic
              </label>
              <input
                id="topic"
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder={`Type a topic (e.g., ${rotatingExamples[placeholderIndex]})`}
                className={cn(
                  "w-full text-center placeholder:text-grey/70",
                  "bg-transparent outline-none",
                  "text-4xl md:text-6xl font-semibold tracking-tight text-charcoal",
                  "py-3"
                )}
                style={{ caretColor: "var(--emerald)" }}
              />
              <div className="h-px w-full bg-[var(--color-border)]" />
              <div className="flex flex-col items-center gap-5 w-full">
                <div className="text-xs text-grey">
                  Press Enter to confirm or pick a suggestion below
                </div>
                <div className="flex flex-col sm:flex-row items-center gap-3 w-full justify-center">
                  <button
                    type="button"
                    aria-label="Start Learning"
                    disabled={!topic.trim() || aiLoading}
                    onClick={() => {
                      if (!topic.trim()) return;
                      generateLearningPlan(topic.trim());
                    }}
                    className={cn(
                      "inline-flex items-center gap-2 px-5 py-3 rounded-full",
                      "text-sm font-medium transition focus:outline-none",
                      "shadow-sm ring-1",
                      topic.trim() && !aiLoading
                        ? "text-white ring-white/20 bg-[var(--emerald)] hover:bg-[var(--emerald-deep)]"
                        : "text-white/60 ring-white/10 bg-[color:rgb(2_108_74_/_.5)] cursor-not-allowed"
                    )}
                  >
                    {aiLoading ? "Generating Plan..." : "Start Learning"}
                  </button>

                  <div
                    className={cn(
                      "inline-flex items-center gap-1 p-1 rounded-full",
                      "border border-[var(--color-border)] bg-white/70 backdrop-blur",
                      "shadow-sm"
                    )}
                    role="group"
                    aria-label="Learning depth selector"
                  >
                    {[
                      { key: "detailed", label: "Detailed" },
                      { key: "general", label: "General" },
                      { key: "simple", label: "Simple" },
                    ].map(({ key, label }) => {
                      const selected = intensity === (key as typeof intensity);
                      return (
                        <button
                          key={key}
                          type="button"
                          onClick={() => setIntensity(key as typeof intensity)}
                          className={cn(
                            "px-3.5 py-2 rounded-full text-sm transition",
                            selected
                              ? "bg-[var(--emerald)] text-white shadow"
                              : "text-charcoal hover:bg-white"
                          )}
                        >
                          {label}
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="w-full max-w-2xl mx-auto">
                  <label className="text-xs font-medium text-grey block mb-2 select-none">Focus areas (optional)</label>
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        "flex-1 flex items-center gap-2 rounded-full",
                        "border border-[var(--color-border)] bg-white/70",
                        "px-4 py-2.5"
                      )}
                    >
                      <input
                        type="text"
                        value={focusInput}
                        onChange={(e) => setFocusInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            const value = focusInput.trim();
                            if (!value) return;
                            const exists = focusAreas.some(
                              (fa) => fa.toLowerCase() === value.toLowerCase()
                            );
                            if (!exists) setFocusAreas((prev) => [...prev, value]);
                            setFocusInput("");
                          }
                        }}
                        placeholder="Add a focus area (e.g., Data lineage) and press Enter"
                        className="flex-1 bg-transparent outline-none text-sm"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        const value = focusInput.trim();
                        if (!value) return;
                        const exists = focusAreas.some(
                          (fa) => fa.toLowerCase() === value.toLowerCase()
                        );
                        if (!exists) setFocusAreas((prev) => [...prev, value]);
                        setFocusInput("");
                      }}
                      className={cn(
                        "inline-flex items-center justify-center rounded-full",
                        "h-10 w-10 text-sm font-medium",
                        focusInput.trim()
                          ? "bg-[var(--emerald)] text-white hover:bg-[var(--emerald-deep)]"
                          : "bg-[color:rgb(2_108_74_/_.5)] text-white/70 cursor-not-allowed"
                      )}
                      aria-label="Add focus area"
                      disabled={!focusInput.trim()}
                    >
                      +
                    </button>
                  </div>
                  {focusAreas.length > 0 && (
                    <motion.div
                      className="flex flex-wrap gap-2 mt-3"
                      initial="hidden"
                      animate="visible"
                      variants={{
                        hidden: {},
                        visible: { transition: { staggerChildren: 0.05 } },
                      }}
                    >
                      {focusAreas.map((fa) => (
                        <motion.span
                          key={fa}
                          className={cn(
                            "group inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm",
                            "bg-white border border-[var(--color-border)]"
                          )}
                          variants={{ hidden: { opacity: 0, y: 6 }, visible: { opacity: 1, y: 0 } }}
                        >
                          <span className="text-charcoal">{fa}</span>
                          <button
                            type="button"
                            aria-label={`Remove ${fa}`}
                            onClick={() =>
                              setFocusAreas((prev) => prev.filter((x) => x !== fa))
                            }
                            className="rounded-full h-5 w-5 inline-flex items-center justify-center text-grey hover:text-black hover:bg-gray-100"
                          >
                            ×
                          </button>
                        </motion.span>
                      ))}
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Suggestions */}
        <div className="mt-10">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-semibold text-charcoal">Suggestions</h3>
            {loading && <span className="text-xs text-grey">Loading…</span>}
          </div>

          {groups.length === 0 ? (
            <div className="text-grey text-sm">
              No categories configured yet for this certificate.
            </div>
          ) : (
            <motion.div
              className="flex flex-wrap gap-2"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: {},
                visible: {
                  transition: { staggerChildren: 0.05 },
                },
              }}
            >
              {groups.map((g) => (
                <motion.button
                  key={g}
                  type="button"
                  disabled={aiLoading}
                  onClick={() => {
                    if (!framework || aiLoading) return;

                    setTopic(g);
                    generateLearningPlan(g);
                  }}
                  className={cn(
                    "px-3.5 py-2 rounded-full text-sm",
                    "bg-white text-charcoal border border-[var(--color-border)]",
                    "hover:border-[var(--emerald)] hover:text-black transition",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    topic === g ? "ring-2 ring-[var(--emerald)]" : ""
                  )}
                  variants={{ hidden: { opacity: 0, y: 6 }, visible: { opacity: 1, y: 0 } }}
                  whileTap={{ scale: 0.98 }}
                >
                  {aiLoading && selectedTopic === g ? "Generating..." : g}
                </motion.button>
              ))}
            </motion.div>
          )}
        </div>
      </div>

      {/* AI Loading Popup */}
      {aiLoading && (
        <motion.div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: "spring", duration: 0.5 }}
          >
            <div className="mb-6">
              <DtcDotLoader isPlaying={true} duration={600} className="mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">
              Generating Learning Plan
            </h3>
            <p className="text-[var(--grey)] mb-4">
              AI is crafting your personalized learning journey for "{selectedTopic}"
            </p>
            <div className="text-sm text-[var(--grey)]">
              This may take a few moments...
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}

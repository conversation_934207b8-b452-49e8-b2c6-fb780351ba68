"use client";

import { ReactNode } from "react";
import { motion } from "framer-motion";

interface LearningLayoutProps {
  children: ReactNode;
}

export default function LearningLayout({ children }: LearningLayoutProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen bg-gradient-to-br from-white via-white to-emerald/5"
    >
      <div className="learning-hub-container">
        {children}
      </div>
    </motion.div>
  );
}

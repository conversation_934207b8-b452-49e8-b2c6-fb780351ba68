"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useLocale } from "next-intl";
import { motion } from "framer-motion";
import { ArrowLeft, Brain, Clock, Target, CheckCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import LearningTimeline from "@/components/ui/learning/LearningTimeline";
import { dtcAssets } from "@/lib/assets";

interface LearningPlan {
  topic: string;
  intensity: "simple" | "general" | "detailed";
  focusAreas: string[];
  overview: {
    title: string;
    description: string;
    totalSteps: number;
    estimatedDuration: string;
    difficultyProgression: string;
  };
  learningPath: Array<{
    id: string;
    title: string;
    description: string;
    difficulty: "beginner" | "intermediate" | "advanced";
    estimatedTime: string;
    prerequisites: string[];
    keyTopics: string[];
    practicalExercises: string[];
    resources: string[];
    icon?: string;
  }>;
  milestones: Array<{
    step: number;
    title: string;
    description: string;
  }>;
  nextActions: string[];
}

export default function LearningConceptPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  
  const framework = Array.isArray(params?.framework) 
    ? params?.framework[0] 
    : (params?.framework as string | undefined);
    
  const concept = Array.isArray(params?.concept) 
    ? params?.concept[0] 
    : (params?.concept as string | undefined);

  const [learningPlan, setLearningPlan] = useState<LearningPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Decode the concept name and remove the "C" prefix if it exists
  const decodedConcept = concept ? decodeURIComponent(concept).replace(/^C/, '') : "";

  useEffect(() => {
    async function fetchLearningPlan() {
      if (!concept || !framework) return;
      
      try {
        setLoading(true);
        setError(null);
        
        // For now, we'll generate a new plan each time
        // In a real app, you might want to cache this or store it
        const response = await fetch('/api/ai/learning/plan', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            topic: decodedConcept,
            intensity: "general", // Default intensity
            focusAreas: [],
            framework
          })
        });

        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch learning plan');
        }

        if (!result.success) {
          throw new Error(result.error || 'Failed to generate learning plan');
        }

        setLearningPlan(result.data);
      } catch (err) {
        console.error('Error fetching learning plan:', err);
        setError(err instanceof Error ? err.message : 'Failed to load learning plan');
      } finally {
        setLoading(false);
      }
    }

    fetchLearningPlan();
  }, [concept, framework, decodedConcept]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-[var(--color-muted)] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[var(--emerald)] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[var(--charcoal)] font-medium">Generating your personalized learning plan...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-[var(--color-muted)] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Brain className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-[var(--charcoal)] mb-2">Unable to Generate Learning Plan</h2>
          <p className="text-[var(--grey)] mb-4">{error}</p>
          <button
            onClick={() => router.back()}
            className="inline-flex items-center gap-2 px-4 py-2 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition"
          >
            <ArrowLeft className="w-4 h-4" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!learningPlan) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-[var(--color-muted)]">
      {/* Header */}
      <div className="bg-gradient-to-r from-[var(--emerald)] to-[var(--emerald-deep)] text-white">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-white/10 rounded-lg transition"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <img 
              src={dtcAssets.logoBlack} 
              alt="DTC Logo" 
              className="h-8 w-auto filter brightness-0 invert"
            />
            <span className="text-white/80">Learning Hub</span>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <motion.h1 
                className="text-3xl md:text-4xl font-bold mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                {learningPlan.overview.title}
              </motion.h1>
              <motion.p 
                className="text-white/90 text-lg mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                {learningPlan.overview.description}
              </motion.p>
              
              <motion.div 
                className="flex flex-wrap gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center gap-2 bg-white/10 px-3 py-2 rounded-lg">
                  <Target className="w-4 h-4" />
                  <span className="text-sm">{learningPlan.overview.totalSteps} Steps</span>
                </div>
                <div className="flex items-center gap-2 bg-white/10 px-3 py-2 rounded-lg">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">{learningPlan.overview.estimatedDuration}</span>
                </div>
                <div className="flex items-center gap-2 bg-white/10 px-3 py-2 rounded-lg">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">{framework} Certification</span>
                </div>
              </motion.div>
            </div>
            
            <motion.div 
              className="hidden md:block"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="relative">
                <div className="w-64 h-64 bg-white/10 rounded-full flex items-center justify-center mx-auto">
                  <Brain className="w-32 h-32 text-white/80" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Learning Timeline */}
      <div className="max-w-7xl mx-auto px-6 py-12">
        <LearningTimeline 
          learningPath={learningPlan.learningPath}
          milestones={learningPlan.milestones}
        />
      </div>
    </div>
  );
}

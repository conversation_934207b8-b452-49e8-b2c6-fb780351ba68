"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useLocale } from "next-intl";
import { motion } from "framer-motion";
import { ArrowLeft, Brain, Clock, Target, CheckCircle, Play, Edit3 } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import LearningTimeline from "@/components/ui/learning/LearningTimeline";
import { DtcHero } from "@/components/ui/Hero";
import { DtcDotLoader } from "@/components/ui/DtcDotLoader";
import { dtcAssets } from "@/lib/assets";

interface LearningPlan {
  topic: string;
  intensity: "simple" | "general" | "detailed";
  focusAreas: string[];
  overview: {
    title: string;
    description: string;
    totalSteps: number;
    estimatedDuration: string;
    difficultyProgression: string;
  };
  learningPath: Array<{
    id: string;
    title: string;
    description: string;
    difficulty: "beginner" | "intermediate" | "advanced";
    estimatedTime: string;
    prerequisites: string[];
    keyTopics: string[];
    practicalExercises: string[];
    resources: string[];
    icon?: string;
  }>;
  milestones: Array<{
    step: number;
    title: string;
    description: string;
  }>;
  nextActions: string[];
}

export default function LearningConceptPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  
  const framework = Array.isArray(params?.framework) 
    ? params?.framework[0] 
    : (params?.framework as string | undefined);
    
  const concept = Array.isArray(params?.concept) 
    ? params?.concept[0] 
    : (params?.concept as string | undefined);

  const [learningPlan, setLearningPlan] = useState<LearningPlan | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // Decode the concept name and remove the "C" prefix if it exists
  const decodedConcept = concept ? decodeURIComponent(concept).replace(/^C/, '') : "";

  useEffect(() => {
    async function fetchLearningPlan() {
      if (!concept || !framework) return;

      try {
        setError(null);

        const response = await fetch('/api/ai/learning/plan', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            topic: decodedConcept,
            intensity: "general", // Default intensity
            focusAreas: [],
            framework
          })
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch learning plan');
        }

        if (!result.success) {
          throw new Error(result.error || 'AI rejected the topic as not certificate-related');
        }

        setLearningPlan(result.data);
      } catch (err) {
        console.error('Error fetching learning plan:', err);
        setError(err instanceof Error ? err.message : 'Failed to load learning plan');
      }
    }

    fetchLearningPlan();
  }, [concept, framework, decodedConcept]);

  // Show nothing while loading (loading handled in previous page)
  if (!learningPlan && !error) {
    return null;
  }

  if (error) {
    return (
      <div className="space-y-6">
        <DtcHero
          title="Learning Plan Generation Failed"
          subtitle="We encountered an issue while creating your learning plan"
          image="hero1"
        />

        <div className="max-w-4xl mx-auto px-6">
          <div className="dtc-card rounded-2xl p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Brain className="w-8 h-8 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">Unable to Generate Learning Plan</h3>
            <p className="text-[var(--grey)] mb-6">{error}</p>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center gap-2 px-6 py-3 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Learning Hub
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DtcHero
        title={learningPlan.overview.title}
        subtitle={learningPlan.overview.description}
        image="hero1"
      >
        {/* Back Button and Stats */}
        <div className="flex flex-col sm:flex-row items-center gap-4 mt-6">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-lg hover:bg-white/20 transition-colors border border-white/20"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Learning Hub
          </button>

          <div className="flex flex-wrap gap-3">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <Target className="w-4 h-4" />
              <span className="text-sm">{learningPlan.overview.totalSteps} Steps</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{learningPlan.overview.estimatedDuration}</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">{framework?.toUpperCase()} Certification</span>
            </div>
            <button
              onClick={() => setShowEditModal(true)}
              className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20 hover:bg-white/20 transition-colors"
            >
              <Edit3 className="w-4 h-4" />
              <span className="text-sm">Edit Plan</span>
            </button>
          </div>
        </div>
      </DtcHero>

      {/* Learning Timeline */}
      <div className="max-w-7xl mx-auto px-6">
        <LearningTimeline
          learningPath={learningPlan.learningPath}
          milestones={learningPlan.milestones}
        />
      </div>

      {/* Edit Modal */}
      {showEditModal && (
        <EditPlanModal
          learningPlan={learningPlan}
          onClose={() => setShowEditModal(false)}
          onSave={(updatedPlan) => {
            setLearningPlan(updatedPlan);
            setShowEditModal(false);
          }}
        />
      )}
    </div>
  );
}

// Edit Plan Modal Component
interface EditPlanModalProps {
  learningPlan: LearningPlan;
  onClose: () => void;
  onSave: (updatedPlan: LearningPlan) => void;
}

function EditPlanModal({ learningPlan, onClose, onSave }: EditPlanModalProps) {
  const [editedPlan, setEditedPlan] = useState<LearningPlan>(learningPlan);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-[var(--color-border)]">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-[var(--charcoal)]">Edit Learning Plan</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-[var(--color-muted)] rounded-lg transition"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Overview Section */}
          <div>
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-3">Overview</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-[var(--charcoal)] mb-1">Title</label>
                <input
                  type="text"
                  value={editedPlan.overview.title}
                  onChange={(e) => setEditedPlan({
                    ...editedPlan,
                    overview: { ...editedPlan.overview, title: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--charcoal)] mb-1">Description</label>
                <textarea
                  value={editedPlan.overview.description}
                  onChange={(e) => setEditedPlan({
                    ...editedPlan,
                    overview: { ...editedPlan.overview, description: e.target.value }
                  })}
                  rows={3}
                  className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]"
                />
              </div>
            </div>
          </div>

          {/* Learning Steps */}
          <div>
            <h3 className="text-lg font-semibold text-[var(--charcoal)] mb-3">Learning Steps</h3>
            <div className="space-y-4">
              {editedPlan.learningPath.map((step, index) => (
                <div key={step.id} className="border border-[var(--color-border)] rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-[var(--charcoal)]">Step {index + 1}</h4>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-1">Title</label>
                      <input
                        type="text"
                        value={step.title}
                        onChange={(e) => {
                          const updatedSteps = [...editedPlan.learningPath];
                          updatedSteps[index] = { ...step, title: e.target.value };
                          setEditedPlan({ ...editedPlan, learningPath: updatedSteps });
                        }}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[var(--charcoal)] mb-1">Description</label>
                      <textarea
                        value={step.description}
                        onChange={(e) => {
                          const updatedSteps = [...editedPlan.learningPath];
                          updatedSteps[index] = { ...step, description: e.target.value };
                          setEditedPlan({ ...editedPlan, learningPath: updatedSteps });
                        }}
                        rows={2}
                        className="w-full px-3 py-2 border border-[var(--color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--emerald)]"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-[var(--color-border)] flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-[var(--charcoal)] border border-[var(--color-border)] rounded-lg hover:bg-[var(--color-muted)] transition"
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(editedPlan)}
            className="px-6 py-2 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
}

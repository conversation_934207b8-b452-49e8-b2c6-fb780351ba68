"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useLocale } from "next-intl";
import { motion } from "framer-motion";
import { ArrowLeft, Brain, Clock, Target, CheckCircle, Edit3 } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import LearningTimeline from "@/components/ui/learning/LearningTimeline";
import { DtcHero } from "@/components/ui/Hero";
import EditPlanModal from "@/components/ui/learning/EditPlanModal";
import { dtcAssets } from "@/lib/assets";

interface LearningPlan {
  topic: string;
  intensity: "simple" | "general" | "detailed";
  focusAreas: string[];
  overview: {
    title: string;
    description: string;
    totalSteps: number;
    estimatedDuration: string;
    difficultyProgression: string;
  };
  learningPath: Array<{
    id: string;
    title: string;
    description: string;
    difficulty: "beginner" | "intermediate" | "advanced";
    estimatedTime: string;
    prerequisites: string[];
    keyTopics: string[];
    practicalExercises: string[];
    resources: string[];
    icon?: string;
  }>;
  milestones: Array<{
    step: number;
    title: string;
    description: string;
  }>;
  nextActions: string[];
}

export default function LearningConceptPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  
  const framework = Array.isArray(params?.framework) 
    ? params?.framework[0] 
    : (params?.framework as string | undefined);
    
  const concept = Array.isArray(params?.concept) 
    ? params?.concept[0] 
    : (params?.concept as string | undefined);

  const [learningPlan, setLearningPlan] = useState<LearningPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Decode the concept name and remove the "C" prefix if it exists
  const decodedConcept = concept ? decodeURIComponent(concept).replace(/^C/, '') : "";

  useEffect(() => {
    async function generateNewPlan() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/ai/learning/plan', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            topic: decodedConcept,
            intensity: "general",
            focusAreas: [],
            framework
          })
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to generate learning plan');
        }

        if (!result.success) {
          throw new Error(result.error || 'Failed to generate learning plan');
        }

        setLearningPlan(result.data);
      } catch (err) {
        console.error('Error generating learning plan:', err);
        setError(err instanceof Error ? err.message : 'Failed to generate learning plan');
      } finally {
        setLoading(false);
      }
    }

    function loadLearningPlan() {
      if (!concept || !framework) return;

      try {
        setLoading(true);
        setError(null);

        // Get the plan from sessionStorage
        const storedPlan = sessionStorage.getItem('learningPlan');
        console.log('Stored plan:', storedPlan ? 'Found' : 'Not found'); // Debug log
        console.log('Concept:', decodedConcept, 'Framework:', framework); // Debug log

        if (storedPlan) {
          try {
            const plan = JSON.parse(storedPlan);
            console.log('Parsed plan:', plan); // Debug log
            setLearningPlan(plan);
            // Clear the stored plan after loading
            sessionStorage.removeItem('learningPlan');
            setLoading(false);
          } catch (parseError) {
            console.error('Error parsing stored plan:', parseError);
            generateNewPlan();
          }
        } else {
          // If no plan in sessionStorage, try to generate one
          console.log('No stored plan found, generating new one for:', decodedConcept);
          generateNewPlan();
        }
      } catch (err) {
        console.error('Error loading learning plan:', err);
        setError(err instanceof Error ? err.message : 'Failed to load learning plan');
        setLoading(false);
      }
    }

    loadLearningPlan();
  }, [concept, framework, decodedConcept]);



  const handleSavePlan = (updatedPath: LearningPlan['learningPath']) => {
    if (learningPlan) {
      const updatedPlan = {
        ...learningPlan,
        learningPath: updatedPath,
        overview: {
          ...learningPlan.overview,
          totalSteps: updatedPath.length
        }
      };
      setLearningPlan(updatedPlan);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <DtcHero
          title="Loading Learning Plan"
          subtitle="Preparing your personalized learning experience..."
          image="hero1"
        >
          <div className="flex items-center justify-center mt-6">
            <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin"></div>
          </div>
        </DtcHero>

        <div className="max-w-4xl mx-auto px-6">
          <div className="dtc-card rounded-2xl p-8 text-center">
            <Brain className="w-16 h-16 text-[var(--emerald)] mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">Loading your learning journey</h3>
            <p className="text-[var(--grey)]">
              Preparing "{decodedConcept}" learning plan for {framework?.toUpperCase()} certification.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <DtcHero
          title="Learning Plan Generation Failed"
          subtitle="We encountered an issue while creating your learning plan"
          image="hero1"
        />

        <div className="max-w-4xl mx-auto px-6">
          <div className="dtc-card rounded-2xl p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Brain className="w-8 h-8 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-[var(--charcoal)] mb-2">Unable to Generate Learning Plan</h3>
            <p className="text-[var(--grey)] mb-6">{error}</p>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center gap-2 px-6 py-3 bg-[var(--emerald)] text-white rounded-lg hover:bg-[var(--emerald-deep)] transition font-medium"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Learning Hub
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!learningPlan) {
    return null;
  }

  return (
    <div className="space-y-6">
      <DtcHero
        title={learningPlan.overview.title}
        subtitle={learningPlan.overview.description}
        image="hero1"
      >
        {/* Back Button and Stats */}
        <div className="flex flex-col sm:flex-row items-center gap-4 mt-6">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-lg hover:bg-white/20 transition-colors border border-white/20"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Learning Hub
          </button>

          <div className="flex flex-wrap gap-3">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <Target className="w-4 h-4" />
              <span className="text-sm">{learningPlan.overview.totalSteps} Steps</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{learningPlan.overview.estimatedDuration}</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">{framework?.toUpperCase()} Certification</span>
            </div>
            <button
              onClick={() => setIsEditModalOpen(true)}
              className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20 hover:bg-white/20 transition-colors"
            >
              <Edit3 className="w-4 h-4" />
              <span className="text-sm">Edit Plan</span>
            </button>
          </div>
        </div>
      </DtcHero>

      {/* Learning Timeline */}
      <div className="max-w-7xl mx-auto px-6">
        <LearningTimeline
          learningPath={learningPlan.learningPath}
          milestones={learningPlan.milestones}
        />
      </div>

      {/* Edit Plan Modal */}
      <EditPlanModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        learningPath={learningPlan.learningPath}
        onSave={handleSavePlan}
      />
    </div>
  );
}

import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";

const LearningPlanRequestSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["simple", "general", "detailed"]).default("general"),
  focusAreas: z.array(z.string()).default([]),
  framework: z.string().min(1),
});

const LearningStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().min(50).max(300),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]),
  estimatedTime: z.string(),
  prerequisites: z.array(z.string()),
  keyTopics: z.array(z.string()).min(2).max(6),
  practicalExercises: z.array(z.string()).min(1).max(4),
  resources: z.array(z.string()).min(1).max(5),
  icon: z.string().optional(),
});

const LearningPlanResponseSchema = z.object({
  topic: z.string(),
  intensity: z.enum(["simple", "general", "detailed"]),
  focusAreas: z.array(z.string()),
  overview: z.object({
    title: z.string(),
    description: z.string().min(100).max(500),
    totalSteps: z.number().min(3).max(15),
    estimatedDuration: z.string(),
    difficultyProgression: z.string(),
  }),
  learningPath: z.array(LearningStepSchema).min(3).max(15),
  milestones: z.array(z.object({
    step: z.number(),
    title: z.string(),
    description: z.string(),
  })).min(2).max(5),
  nextActions: z.array(z.string()).min(2).max(5),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const parsed = LearningPlanRequestSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json(
        { error: "Invalid request", details: parsed.error.issues },
        { status: 400 }
      );
    }

    const { topic, intensity, focusAreas, framework } = parsed.data;

    const intensityPrompts = {
      simple: "Create a beginner-friendly learning plan with simple concepts, basic examples, and gentle progression. Focus on fundamental understanding.",
      general: "Design a balanced learning plan suitable for most learners. Include core concepts, practical examples, and moderate complexity.",
      detailed: "Develop a comprehensive, in-depth learning plan with advanced concepts, detailed explanations, and complex real-world applications."
    };

    const focusAreasPrompt = focusAreas.length > 0 
      ? `Pay special attention to these focus areas: ${focusAreas.join(", ")}. Ensure these areas are well-represented throughout the learning path.`
      : "";

    const systemPrompt = `You are an expert learning architect and curriculum designer with deep expertise in creating comprehensive, structured learning journeys for professional certification preparation. Your task is to create a detailed, step-by-step learning plan that takes learners from A-Z mastery of certification-related topics.

CRITICAL REQUIREMENTS:
1. CERTIFICATE RELEVANCE CHECK: The topic "${topic}" must be directly related to the "${framework}" certification framework or professional certifications, IT certifications, cybersecurity, data privacy, compliance, or professional development. If the topic is NOT related to these areas (e.g., cooking, sports, entertainment, personal hobbies), you must REFUSE to create a plan and return an error.

2. FRAMEWORK CONTEXT: This learning plan is specifically for the "${framework}" certification. Ensure all content aligns with this certification's objectives and requirements.

3. LEARNING APPROACH: Focus on instant tutoring and practical mastery rather than long academic courses. Create a concise but comprehensive journey that gets learners to competency quickly for their certification goals.

Guidelines:
- Validate that the topic is certificate/professional development related and relevant to ${framework}
- Create 5-12 progressive learning steps (not too long, focused on instant tutoring)
- Each step should build upon previous knowledge and be actionable
- Include practical exercises and real-world scenarios relevant to certification exams
- Provide clear time estimates (favor shorter, focused sessions)
- Structure the plan for optimal learning retention and certification readiness
- ${intensityPrompts[intensity]}
- ${focusAreasPrompt}

If the topic is not related to certifications, professional development, or the ${framework} certification framework, respond with an error indicating the topic is outside the scope of certificate-related learning.

The learning plan should be comprehensive, actionable, and designed to create true mastery of the subject matter for certification success.`;

    const userPrompt = `Create a comprehensive A-Z learning plan for mastering: "${topic}"

The plan should include:
1. A compelling overview that explains the learning journey
2. A detailed step-by-step learning path with 5-12 progressive steps
3. Each step should include:
   - Clear title and description
   - Appropriate difficulty level (beginner/intermediate/advanced)
   - Realistic time estimates
   - Prerequisites from previous steps
   - Key topics to master
   - Practical exercises to reinforce learning
   - Recommended resources and materials
4. Key milestones that mark significant progress points
5. Next actions the learner should take to continue their journey

Focus on creating a journey that transforms a complete beginner into someone with deep, practical mastery of ${topic}. Make it engaging, comprehensive, and actionable.

${focusAreas.length > 0 ? `Special focus areas to emphasize: ${focusAreas.join(", ")}` : ""}`;

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      schema: LearningPlanResponseSchema,
      prompt: `${systemPrompt}\n\n${userPrompt}`,
      temperature: 0.8,
    });

    return Response.json({
      success: true,
      data: result.object,
    });

  } catch (error) {
    console.error("Error generating learning plan:", error);
    return Response.json(
      { error: "Failed to generate learning plan" },
      { status: 500 }
    );
  }
}

import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject, generateText } from "ai";
import { z } from "zod";

const LearningPlanRequestSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["simple", "general", "detailed"]).default("general"),
  focusAreas: z.array(z.string()).default([]),
  framework: z.string().min(1),
  certificateDetails: z.object({
    name: z.string(),
    provider: z.string(),
    description: z.string(),
    domain: z.string(),
    questionType: z.string(),
  }).optional(),
});

const LearningStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().min(20).max(500),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]),
  estimatedTime: z.string(),
  prerequisites: z.array(z.string()),
  keyTopics: z.array(z.string()).min(1).max(8),
  practicalExercises: z.array(z.string()).min(1).max(6),
  resources: z.array(z.string()).min(1).max(8),
  icon: z.string().optional(),
});

const LearningPlanResponseSchema = z.object({
  topic: z.string(),
  intensity: z.enum(["simple", "general", "detailed"]),
  focusAreas: z.array(z.string()),
  overview: z.object({
    title: z.string(),
    description: z.string().min(20).max(400),
    totalSteps: z.number().min(3).max(8),
    estimatedDuration: z.string(),
    difficultyProgression: z.string(),
  }),
  learningPath: z.array(LearningStepSchema).min(3).max(8),
  milestones: z.array(z.object({
    step: z.number(),
    title: z.string(),
    description: z.string(),
  })).min(1).max(5),
  nextActions: z.array(z.string()).min(1).max(8),
});

export async function POST(req: NextRequest) {
  let topic = "";

  try {
    const body = await req.json();
    const parsed = LearningPlanRequestSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json(
        { error: "Invalid request", details: parsed.error.issues },
        { status: 400 }
      );
    }

    const { topic: requestTopic, intensity, focusAreas, framework, certificateDetails } = parsed.data;
    topic = requestTopic;

    // Use provided certificate details or create default ones
    const certDetails = certificateDetails || {
      name: framework.toUpperCase(),
      provider: "Professional Certification Body",
      description: `Professional certification in ${framework}`,
      domain: "professional_development",
      questionType: "multiple_choice"
    };

    const intensityPrompts = {
      simple: "Create a beginner-friendly, single-session learning plan (30-45 minutes) with simple concepts and basic examples. Focus on fundamental understanding that can be completed in one sitting.",
      general: "Design a balanced, single-session learning plan (45-60 minutes) suitable for most learners. Include core concepts and practical examples that can be mastered in one focused session.",
      detailed: "Develop a comprehensive, single-session learning plan (60-90 minutes) with advanced concepts and detailed explanations. Ensure all content can be absorbed in one intensive study session."
    };

    const focusAreasPrompt = focusAreas.length > 0 
      ? `Pay special attention to these focus areas: ${focusAreas.join(", ")}. Ensure these areas are well-represented throughout the learning path.`
      : "";

    const systemPrompt = `You are an expert learning architect and curriculum designer with deep expertise in creating comprehensive, structured learning journeys for professional certification preparation. Your task is to create a detailed, step-by-step learning plan that takes learners from A-Z mastery of certification-related topics.

CERTIFICATE CONTEXT:
- Certificate Name: ${certDetails.name}
- Provider: ${certDetails.provider}
- Description: ${certDetails.description}
- Domain: ${certDetails.domain}
- Question Type: ${certDetails.questionType}

CRITICAL REQUIREMENTS:
1. STRICT CERTIFICATE RELEVANCE: The topic "${topic}" has already been pre-validated as certificate-related. However, if during plan generation you determine this topic is NOT directly related to professional certifications (IT, cybersecurity, data privacy, compliance, project management, cloud computing, data science, enterprise architecture), you MUST refuse to create a plan. Topics like cooking, sports, entertainment, personal hobbies, general business, or non-certification education are STRICTLY FORBIDDEN.

2. FRAMEWORK CONTEXT: This learning plan is specifically for the "${certDetails.name}" certification by ${certDetails.provider}. The certificate focuses on ${certDetails.domain} domain. Ensure all content aligns with this certification's objectives, requirements, and the specific domain focus.

3. QUESTION TYPE ALIGNMENT: The certificate uses ${certDetails.questionType} question format. Tailor practical exercises and examples to match this assessment style.

4. LEARNING APPROACH: Focus on SINGLE-SESSION instant tutoring and practical mastery. Create a concise learning plan that can be completed in ONE SITTING (30-90 minutes max). This is about understanding a concept quickly, not a long course.

Guidelines:
- Validate that the topic is certificate/professional development related and relevant to ${certDetails.name}
- Create 3-6 progressive learning steps (focused for single-session completion)
- Each step should be concise and build upon previous knowledge
- Include practical exercises and real-world scenarios relevant to ${certDetails.name} certification exams
- Tailor examples and use cases to the ${certDetails.domain} domain
- Design exercises that match the ${certDetails.questionType} assessment format
- Reference ${certDetails.provider} standards and best practices where applicable
- Provide realistic time estimates for single-session learning
- Structure the plan for rapid understanding and immediate application
- ${intensityPrompts[intensity]}
- ${focusAreasPrompt}

If the topic is not related to certifications, professional development, or the ${certDetails.name} certification framework, respond with an error indicating the topic is outside the scope of certificate-related learning.

The learning plan should be comprehensive, actionable, and designed to create true mastery of the subject matter for ${certDetails.name} certification success. All content should be tailored to the ${certDetails.domain} domain and ${certDetails.provider} standards.`;

    const userPrompt = `Create a comprehensive A-Z learning plan for mastering: "${topic}"

The plan should include:
1. A compelling overview that explains the single-session learning journey (30-90 minutes total)
   - CRITICAL: Keep the overview description UNDER 400 characters (maximum 2-3 short sentences)
   - Count characters carefully - this is a strict requirement
2. A concise step-by-step learning path with 3-6 progressive steps
3. Each step should include:
   - Clear title and description (keep descriptions concise)
   - Appropriate difficulty level (beginner/intermediate/advanced)
   - Short time estimates (5-15 minutes per step)
   - Prerequisites from previous steps (use step IDs like "step_1", "step_2")
   - Key topics to master quickly (1-6 items max)
   - Quick practical exercises to reinforce learning (1-6 items max)
   - Essential resources for immediate application (1-8 items max)
4. 1-3 key milestones that mark significant progress points
5. Next actions for immediate application or further study (1-8 items max)

Focus on creating a SINGLE-SESSION learning experience that gives someone solid understanding and practical knowledge of ${topic} in one focused study session. Make it engaging, concise, and immediately actionable for ${certDetails.name} certification preparation.

Ensure all content is specifically tailored to:
- ${certDetails.name} certification objectives
- ${certDetails.provider} standards and methodologies
- ${certDetails.domain} domain expertise
- ${certDetails.questionType} assessment format

${focusAreas.length > 0 ? `Special focus areas to emphasize: ${focusAreas.join(", ")}` : ""}

CRITICAL: Ensure your response strictly follows the required format and character limits. The overview description must be under 400 characters, and all arrays must respect their maximum limits.`;

    console.log("Generating learning plan for:", topic);

    // STRICT relevance check - AI must analyze and justify if topic is certificate-related
    const { text: relevanceAnalysis } = await generateText({
      model: google("gemini-2.5-flash"),
      prompt: `STRICT ANALYSIS REQUIRED: Analyze if the topic "${topic}" is directly related to professional certifications.

ACCEPTABLE TOPICS ONLY:
- IT Certifications (CompTIA, Cisco, Microsoft, AWS, etc.)
- Cybersecurity Certifications (CISSP, CISM, CEH, etc.)
- Data Privacy & Compliance (GDPR, CCPA, CIPP, etc.)
- Project Management (PMP, Agile, Scrum, etc.)
- Data Science & Analytics certifications
- Cloud Computing certifications
- Enterprise Architecture certifications
- IT Governance & Risk Management certifications

UNACCEPTABLE TOPICS (REJECT THESE):
- Cooking, food, recipes, restaurants
- Sports, games, entertainment, movies, music
- Personal hobbies, crafts, art, fashion
- Travel, tourism, vacation planning
- Personal relationships, dating, family
- General education topics not tied to professional certifications
- Business topics not specifically related to certification frameworks

INSTRUCTIONS:
1. First, state clearly: "ANALYSIS:"
2. Then analyze if "${topic}" fits into acceptable certification categories
3. Provide specific reasoning
4. End with either "VERDICT: ACCEPT" or "VERDICT: REJECT"

Be STRICT - only accept topics that are clearly and directly related to professional certification frameworks.`,
      temperature: 0.1,
    });

    console.log("Relevance analysis:", relevanceAnalysis);

    if (relevanceAnalysis.toLowerCase().includes('verdict: reject')) {
      return Response.json({
        success: false,
        error: `"${topic}" is not related to professional certifications. Our AI learning system is designed specifically for certification preparation topics like IT, cybersecurity, cloud computing, data science, project management, and compliance frameworks.`
      }, { status: 400 });
    }

    console.log("Generating learning plan with AI...");

    const result = await generateObject({
      model: google("gemini-2.5-flash"),
      schema: LearningPlanResponseSchema,
      prompt: `${systemPrompt}\n\n${userPrompt}`,
      temperature: 0.7,
    });

    console.log("Generated plan successfully:", {
      topic: result.object.topic,
      stepsCount: result.object.learningPath.length,
      descriptionLength: result.object.overview.description.length
    });

    return Response.json({
      success: true,
      data: result.object,
    });

  } catch (error) {
    console.error("Error generating learning plan:", error);
    console.error("Error details:", {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      topic: topic
    });

    // Handle validation errors specifically
    let errorMessage = "Failed to generate learning plan";
    if (error instanceof Error) {
      if (error.message.includes("too_big") || error.message.includes("ZodError")) {
        console.log("Schema validation failed - content too detailed");
        errorMessage = "The AI generated content that was too detailed. Please try again with a more specific topic.";
      } else if (error.message.includes("response did not match schema")) {
        console.log("Schema validation failed - format mismatch");
        errorMessage = "The AI response format was invalid. Please try again.";
      } else {
        errorMessage = error.message;
      }
    }

    return Response.json(
      {
        error: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}

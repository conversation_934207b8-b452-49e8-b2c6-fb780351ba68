import { NextRequest } from "next/server";
import { google } from "@ai-sdk/google";
import { generateObject, generateText } from "ai";
import { z } from "zod";
import { getCertificate, type CertificateRecord } from "@/Services/certificateDetails";

const LearningPlanRequestSchema = z.object({
  topic: z.string().min(1),
  intensity: z.enum(["simple", "general", "detailed"]).default("general"),
  focusAreas: z.array(z.string()).default([]),
  framework: z.string().min(1),
});

const LearningStepSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().min(20).max(500),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]),
  estimatedTime: z.string(),
  prerequisites: z.array(z.string()),
  keyTopics: z.array(z.string()).min(1).max(8),
  practicalExercises: z.array(z.string()).min(1).max(6),
  resources: z.array(z.string()).min(1).max(8),
  icon: z.string().optional(),
});

const LearningPlanResponseSchema = z.object({
  topic: z.string(),
  intensity: z.enum(["simple", "general", "detailed"]),
  focusAreas: z.array(z.string()),
  overview: z.object({
    title: z.string(),
    description: z.string().min(20).max(400),
    totalSteps: z.number().min(3).max(8),
    estimatedDuration: z.string(),
    difficultyProgression: z.string(),
  }),
  learningPath: z.array(LearningStepSchema).min(3).max(8),
  milestones: z.array(z.object({
    step: z.number(),
    title: z.string(),
    description: z.string(),
  })).min(1).max(5),
  nextActions: z.array(z.string()).min(1).max(8),
});

export async function POST(req: NextRequest) {
  let topic = "";

  try {
    const body = await req.json();
    const parsed = LearningPlanRequestSchema.safeParse(body);

    if (!parsed.success) {
      return Response.json(
        { error: "Invalid request", details: parsed.error.issues },
        { status: 400 }
      );
    }

    const { topic: requestTopic, intensity, focusAreas, framework } = parsed.data;
    topic = requestTopic;

    // Fetch certificate details to tailor the learning plan
    const certificateDetails = await getCertificate(framework);
    if (!certificateDetails) {
      return Response.json(
        { error: `Certificate framework "${framework}" not found`, success: false },
        { status: 404 }
      );
    }

    const intensityPrompts = {
      simple: "Create a beginner-friendly, single-session learning plan (30-45 minutes) with simple concepts and basic examples. Focus on fundamental understanding that can be completed in one sitting.",
      general: "Design a balanced, single-session learning plan (45-60 minutes) suitable for most learners. Include core concepts and practical examples that can be mastered in one focused session.",
      detailed: "Develop a comprehensive, single-session learning plan (60-90 minutes) with advanced concepts and detailed explanations. Ensure all content can be absorbed in one intensive study session."
    };

    const focusAreasPrompt = focusAreas.length > 0 
      ? `Pay special attention to these focus areas: ${focusAreas.join(", ")}. Ensure these areas are well-represented throughout the learning path.`
      : "";

    const systemPrompt = `You are an expert learning architect and curriculum designer with deep expertise in creating comprehensive, structured learning journeys for professional certification preparation. Your task is to create a detailed, step-by-step learning plan that takes learners from A-Z mastery of certification-related topics.

CERTIFICATE CONTEXT:
- Certificate Name: ${certificateDetails.name}
- Provider: ${certificateDetails.provider}
- Description: ${certificateDetails.description}
- Domain: ${certificateDetails.domain}
- Question Type: ${certificateDetails.questionType}

CRITICAL REQUIREMENTS:
1. CERTIFICATE RELEVANCE CHECK: The topic "${topic}" must be directly related to the "${certificateDetails.name}" certification (${certificateDetails.provider}) or professional certifications, IT certifications, cybersecurity, data privacy, compliance, or professional development. If the topic is NOT related to these areas (e.g., cooking, sports, entertainment, personal hobbies), you must REFUSE to create a plan and return an error.

2. FRAMEWORK CONTEXT: This learning plan is specifically for the "${certificateDetails.name}" certification by ${certificateDetails.provider}. The certificate focuses on ${certificateDetails.domain} domain. Ensure all content aligns with this certification's objectives, requirements, and the specific domain focus.

3. QUESTION TYPE ALIGNMENT: The certificate uses ${certificateDetails.questionType} question format. Tailor practical exercises and examples to match this assessment style.

4. LEARNING APPROACH: Focus on SINGLE-SESSION instant tutoring and practical mastery. Create a concise learning plan that can be completed in ONE SITTING (30-90 minutes max). This is about understanding a concept quickly, not a long course.

Guidelines:
- Validate that the topic is certificate/professional development related and relevant to ${certificateDetails.name}
- Create 3-6 progressive learning steps (focused for single-session completion)
- Each step should be concise and build upon previous knowledge
- Include practical exercises and real-world scenarios relevant to ${certificateDetails.name} certification exams
- Tailor examples and use cases to the ${certificateDetails.domain} domain
- Design exercises that match the ${certificateDetails.questionType} assessment format
- Reference ${certificateDetails.provider} standards and best practices where applicable
- Provide realistic time estimates for single-session learning
- Structure the plan for rapid understanding and immediate application
- ${intensityPrompts[intensity]}
- ${focusAreasPrompt}

If the topic is not related to certifications, professional development, or the ${certificateDetails.name} certification framework, respond with an error indicating the topic is outside the scope of certificate-related learning.

The learning plan should be comprehensive, actionable, and designed to create true mastery of the subject matter for ${certificateDetails.name} certification success. All content should be tailored to the ${certificateDetails.domain} domain and ${certificateDetails.provider} standards.`;

    const userPrompt = `Create a comprehensive A-Z learning plan for mastering: "${topic}"

The plan should include:
1. A compelling overview that explains the single-session learning journey (30-90 minutes total)
   - IMPORTANT: Keep the overview description under 400 characters (about 2-3 sentences)
2. A concise step-by-step learning path with 3-6 progressive steps
3. Each step should include:
   - Clear title and description
   - Appropriate difficulty level (beginner/intermediate/advanced)
   - Short time estimates (5-15 minutes per step)
   - Prerequisites from previous steps
   - Key topics to master quickly
   - Quick practical exercises to reinforce learning
   - Essential resources for immediate application
4. 1-3 key milestones that mark significant progress points
5. Next actions for immediate application or further study

Focus on creating a SINGLE-SESSION learning experience that gives someone solid understanding and practical knowledge of ${topic} in one focused study session. Make it engaging, concise, and immediately actionable.

${focusAreas.length > 0 ? `Special focus areas to emphasize: ${focusAreas.join(", ")}` : ""}`;

    console.log("Generating learning plan for:", topic);

    // First, do a quick relevance check using generateText
    const { text: relevanceCheck } = await generateText({
      model: google("gemini-2.5-flash"),
      prompt: `Is the topic "${topic}" directly related to professional certifications, IT certifications, cybersecurity, data privacy, compliance, or professional development? Answer only "YES" or "NO" followed by a brief reason.`,
      temperature: 0.1,
    });

    if (relevanceCheck.toLowerCase().startsWith('no')) {
      return Response.json({
        success: false,
        error: `"${topic}" is not related to professional certifications. Please enter a topic related to IT, cybersecurity, cloud computing, data science, project management, or other professional certification areas.`
      }, { status: 400 });
    }

    const result = await generateObject({
      model: google("gemini-2.5-flash"),
      schema: LearningPlanResponseSchema,
      prompt: `${systemPrompt}\n\n${userPrompt}`,
      temperature: 0.7,
    });

    console.log("Generated plan successfully");

    return Response.json({
      success: true,
      data: result.object,
    });

  } catch (error) {
    console.error("Error generating learning plan:", error);

    // Handle validation errors specifically
    let errorMessage = "Failed to generate learning plan";
    if (error instanceof Error) {
      if (error.message.includes("too_big") || error.message.includes("ZodError")) {
        errorMessage = "The AI generated content that was too detailed. Please try again with a more specific topic.";
      } else if (error.message.includes("response did not match schema")) {
        errorMessage = `"${topic}" may not be suitable for certification learning. Please try a topic related to professional certifications.`;
      } else {
        errorMessage = error.message;
      }
    }

    return Response.json(
      {
        error: errorMessage,
        success: false
      },
      { status: 500 }
    );
  }
}
